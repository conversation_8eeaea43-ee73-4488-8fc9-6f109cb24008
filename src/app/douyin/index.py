import json
from bs4 import BeautifulSoup
import httpx
from src.crawlers.base_crawler import BaseCrawler
from src.crawlers.douyin.endpoints import DouyinAPIEndpoints
from src.crawlers.douyin.util import AwemeIdFetcher, BogusManager
from src.crawlers.util import PostDetail
from src.utils import get_analyze_logger
from src.utils.index import find_url
from src.utils.response import Response
from urllib.parse import urlencode


logger = get_analyze_logger()


class Douyin:
    def __init__(self, text, type):
        self.url = find_url(text)
        self.aweme_id = AwemeIdFetcher.get_aweme_id(self.url)
        self.video_data = self.fetch_one_video(self.aweme_id)
        logger.info(f"video_data: {self.video_data}")

    # 获取单个作品数据
    async def fetch_one_video(self, aweme_id: str):
        # 获取抖音的实时Cookie
        kwargs = await self.get_douyin_headers()
        # 创建一个基础爬虫
        base_crawler = BaseCrawler(proxies=kwargs["proxies"], crawler_headers=kwargs["headers"])
        async with base_crawler as crawler:
            # 创建一个作品详情的BaseModel参数
            params = PostDetail(aweme_id=aweme_id)
            # 生成一个作品详情的带有加密参数的Endpoint
            # 2024年6月12日22:41:44 由于XBogus加密已经失效，所以不再使用XBogus加密参数，转移至a_bogus加密参数。
            # endpoint = BogusManager.xb_model_2_endpoint(
            #     DouyinAPIEndpoints.POST_DETAIL, params.dict(), kwargs["headers"]["User-Agent"]
            # )

            # 生成一个作品详情的带有a_bogus加密参数的Endpoint
            params_dict = params.dict()
            params_dict["msToken"] = ''
            a_bogus = BogusManager.ab_model_2_endpoint(params_dict, kwargs["headers"]["User-Agent"])
            endpoint = f"{DouyinAPIEndpoints.POST_DETAIL}?{urlencode(params_dict)}&a_bogus={a_bogus}"

            response = await crawler.fetch_get_json(endpoint)
        return response

    def to_dict(self):
        """将对象转换为字典，用于 API 返回"""
        try:
            result = {
                "url": self.url,
                "final_url": "",
                "title": self.title,
                "description": self.description,
                "image_list": self.image_list,
                "video": self.video,
                "app_type": "douyin",
            }
            return Response.success(result, "获取成功")
        except Exception as e:
            logger.error(f"抖音转换为字典时出错: {str(e)}", exc_info=True)
            return Response.error("获取失败")
